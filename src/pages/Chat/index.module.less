.chatContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.chatWrapper {
    flex: 1;
    background-color: red;
    height: 100%;
}

.chatOptions {
    width: 30%;
    background-color: pink;
    height: 100%;
}

@media screen and (max-width: 768px) {
  .chatContainer {
    flex-direction: column;
  }

  .chatWrapper {
    width: 100%;
    flex: none;
  }

  .chatOptions {
    display: none;
  }
}
