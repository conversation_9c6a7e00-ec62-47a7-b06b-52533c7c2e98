import { PageContainer } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import { theme } from "antd";
import React from "react";
import c from "./index.module.less";

const Chat: React.FC = () => {
  const { token } = theme.useToken();
  const { initialState } = useModel("@@initialState");
  return (
    <PageContainer>
      <div className={c.chatContainer}>
        <div className={c.chatWrapper}>1</div>
        <div className={c.chatOptions}>2</div>
      </div>
    </PageContainer>
  );
};

export default Chat;
